plugins {
    alias(libs.plugins.vocalbeats.android.library)
    alias(libs.plugins.vocalbeats.hilt)
}

android {
    namespace = "${rootProject.ext["packageName"]}.startup"
}

android {
    defaultConfig {
        buildConfigField("String", "afKey", rootProject.ext["afKey"] as String)
        buildConfigField("String", "afTemplateId", rootProject.ext["afTemplateId"] as String)
        buildConfigField("String", "channelKey", rootProject.ext["channelKey"] as String)
        buildConfigField("String", "productId", rootProject.ext["productId"] as String)
        buildConfigField("String", "lizhiTrackerReportUrl", rootProject.ext["lizhiTrackerReportUrl"] as String)
        buildConfigField("String", "smAppId", rootProject.ext["smAppId"] as String)
        buildConfigField("String", "flashAppId", rootProject.ext["flashAppId"] as String)
    }

    buildFeatures {
        buildConfig = true
    }
}

dependencies {
    api(projects.core.common)
    api(projects.core.data)
    // 引入本地AAR文件
    implementation(files("../local_aar/smsdk_2.9.9_hy_build2.aar"))

}