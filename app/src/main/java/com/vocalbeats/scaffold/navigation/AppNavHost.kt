package com.vocalbeats.scaffold.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.compose.NavHost
import com.vocalbeats.scaffold.feature.feedback.navigation.feedbackScreen
import com.vocalbeats.scaffold.feature.feedback.navigation.navigateToFeedback
import com.vocalbeats.scaffold.feature.settings.navigation.aboutScreen
import com.vocalbeats.scaffold.feature.settings.navigation.navigateToAbout
import com.vocalbeats.scaffold.feature.settings.navigation.navigateToSettings
import com.vocalbeats.scaffold.feature.settings.navigation.settingsScreen
import com.vocalbeats.scaffold.ui.AppState

/**
 * <AUTHOR>
 * @date 2025/8/11
 * @desc
 */
@Composable
fun AppNavHost(
    appState: AppState,
    modifier: Modifier = Modifier,
) {
    val navController = appState.navController
    NavHost(
        navController = navController,
        startDestination = AppRoute,
        modifier = modifier,
    ) {
        homeScreen(
            onNavigateToFeedback = {
                navController.navigateToFeedback()
            },
            onNavigateToSettings = {
                navController.navigateToSettings()
            },
        )

        feedbackScreen(
            onBackClick = {
                navController.popBackStack()
            },
        )

        settingsScreen(
            onBackClick = {
                navController.popBackStack()
            },
            onNavigateToAbout = {
                navController.navigateToAbout()
            },
            onNavigateToFeedback = {
                navController.navigateToFeedback()
            }
        )

        aboutScreen(
            onBackClick = {
                navController.popBackStack()
            },
            onContactUsClick = {
                // TODO: 实现联系我们功能
            },
            onPrivacyPolicyClick = {
                // TODO: 实现隐私政策功能
            },
            onTermsOfUseClick = {
                // TODO: 实现使用条款功能
            }
        )
    }
}