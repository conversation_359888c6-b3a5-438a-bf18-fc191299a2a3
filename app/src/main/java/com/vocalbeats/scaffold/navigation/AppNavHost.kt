package com.vocalbeats.scaffold.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.compose.NavHost
import com.vocalbeats.scaffold.feature.feedback.navigation.feedbackScreen
import com.vocalbeats.scaffold.feature.feedback.navigation.navigateToFeedback
import com.vocalbeats.scaffold.feature.settings.navigation.aboutScreen
import com.vocalbeats.scaffold.feature.settings.navigation.navigateToAbout
import com.vocalbeats.scaffold.feature.settings.navigation.navigateToSettings
import com.vocalbeats.scaffold.feature.settings.navigation.settingsScreen
import com.vocalbeats.scaffold.ui.AppState

/**
 * <AUTHOR>
 * @date 2025/8/11
 * @desc
 */
@Composable
fun AppNavHost(
    appState: AppState,
    modifier: Modifier = Modifier,
) {
    val navController = appState.navController
    NavHost(
        navController = navController,
        startDestination = AppRoute,
        modifier = modifier,
    ) {
        homeScreen(
            onNavToSettings = {
                navController.navigateToSettings()
            },
        )

        feedbackScreen(
            onNavBack = {
                navController.popBackStack()
            },
        )

        settingsScreen(
            onNavBack = {
                navController.popBackStack()
            },
            onNavToAbout = {
                navController.navigateToAbout()
            },
            onNavToFeedback = {
                navController.navigateToFeedback()
            }
        )

        aboutScreen(
            onNavBack = {
                navController.popBackStack()
            },
            onNavToPrivacyPolicy = {
                // TODO: 实现隐私政策功能
            },
            onNavToTermsOfUse = {
                // TODO: 实现使用条款功能
            }
        )
    }
}