package com.vocalbeats.scaffold.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.navigation.compose.NavHost
import com.vocalbeats.scaffold.core.common.R
import com.vocalbeats.scaffold.core.common.ktx.asStringCtx
import com.vocalbeats.scaffold.core.data.appconfig.AppConfigString
import com.vocalbeats.scaffold.core.ui.navigation.navigateToWebView
import com.vocalbeats.scaffold.core.ui.navigation.webViewScreen
import com.vocalbeats.scaffold.feature.feedback.navigation.feedbackScreen
import com.vocalbeats.scaffold.feature.feedback.navigation.navigateToFeedback
import com.vocalbeats.scaffold.feature.settings.navigation.aboutScreen
import com.vocalbeats.scaffold.feature.settings.navigation.navigateToAbout
import com.vocalbeats.scaffold.feature.settings.navigation.navigateToSettings
import com.vocalbeats.scaffold.feature.settings.navigation.settingsScreen
import com.vocalbeats.scaffold.ui.AppState

/**
 * <AUTHOR>
 * @date 2025/8/11
 * @desc
 */
@Composable
fun AppNavHost(
    appState: AppState,
    modifier: Modifier = Modifier,
) {
    val navController = appState.navController
    val context = LocalContext.current
    NavHost(
        navController = navController,
        startDestination = AppRoute,
        modifier = modifier,
    ) {
        homeScreen(
            onNavToSettings = {
                navController.navigateToSettings()
            },
        )

        feedbackScreen(
            onNavBack = {
                navController.popBackStack()
            },
        )

        settingsScreen(
            onNavBack = {
                navController.popBackStack()
            },
            onNavToAbout = {
                navController.navigateToAbout()
            },
            onNavToFeedback = {
                navController.navigateToFeedback()
            }
        )

        aboutScreen(
            onNavBack = {
                navController.popBackStack()
            },
            onNavToPrivacyPolicy = {
                navController.navigateToWebView(
                    url = AppConfigString.PrivacyUrl,
                    title = R.string.about_privacy_policy.asStringCtx(context)
                )
            },
            onNavToTermsOfUse = {
                navController.navigateToWebView(
                    url = AppConfigString.TermsOfServiceUrl,
                    title = R.string.about_terms_of_use.asStringCtx(context)
                )
            }
        )

        // WebView 相关页面
        webViewScreen(
            onBackClick = {
                navController.popBackStack()
            }
        )
    }
}