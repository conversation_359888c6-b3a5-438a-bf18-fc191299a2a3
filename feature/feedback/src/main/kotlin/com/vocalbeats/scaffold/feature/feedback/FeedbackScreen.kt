package com.vocalbeats.scaffold.feature.feedback

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.input.TextFieldLineLimits
import androidx.compose.foundation.text.input.TextFieldState
import androidx.compose.foundation.text.input.rememberTextFieldState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.vocalbeats.designtoken.DesignColors
import com.vocalbeats.designtoken.DesignFonts
import com.vocalbeats.designtoken.DesignIcons
import com.vocalbeats.scaffold.core.common.R
import com.vocalbeats.scaffold.core.common.compose.asString
import com.vocalbeats.scaffold.core.common.compose.debouncedClickable
import com.vocalbeats.scaffold.core.designsystem.component.CommonTextField
import com.vocalbeats.scaffold.core.designsystem.component.IconFontText
import com.yibasan.lizhifm.lzlogan.Logz
import com.yibasan.lizhifm.lzlogan.common.LogzConstant

@Composable
fun FeedbackScreen(
    onBackClick: () -> Unit = {},
    onSubmitClick: (email: String, description: String) -> Unit = { _, _ -> },
    modifier: Modifier = Modifier,
    viewModel: FeedbackViewModel = hiltViewModel(),
) {
    LaunchedEffect(Unit) {
        Logz.send(
            System.currentTimeMillis(), LogzConstant.MODE_4G,
            force = false,
            carry = false
        )
    }

    val emailState = rememberTextFieldState()
    val descriptionState = rememberTextFieldState()

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(DesignColors.white100)
            .statusBarsPadding()
    ) {
        // 顶部导航栏
        FeedbackTopBar(
            isSubmitEnabled = descriptionState.text.isNotBlank(),
            onBackClick = onBackClick,
            onSubmitClick = {
                onSubmitClick(emailState.text.toString(), descriptionState.text.toString())
            },
            modifier = Modifier.fillMaxWidth()
        )

        // 内容区域
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp)
                .padding(top = 32.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp)
        ) {
            // Email 输入区域
            EmailInputSection(
                emailState = emailState
            )

            // Description 输入区域
            DescriptionInputSection(
                descriptionState = descriptionState
            )
        }

        Spacer(modifier = Modifier.weight(1f))

        // 底部滑动控件
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(34.dp),
            contentAlignment = Alignment.Center
        ) {
            Box(
                modifier = Modifier
                    .width(134.dp)
                    .height(5.dp)
                    .clip(RoundedCornerShape(2.5.dp))
                    .background(DesignColors.black20)
            )
        }
    }
}

/**
 * 顶部导航栏组件
 */
@Composable
private fun FeedbackTopBar(
    onBackClick: () -> Unit,
    onSubmitClick: () -> Unit,
    isSubmitEnabled: Boolean,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .height(56.dp)
            .padding(horizontal = 20.dp),
        contentAlignment = Alignment.Center
    ) {
        // 返回按钮
        IconFontText(
            icon = DesignIcons.ArrowLeft,
            size = 24.dp,
            color = DesignColors.black80,
            onClick = onBackClick,
            modifier = Modifier.align(Alignment.CenterStart)
        )

        // 标题
        Text(
            text = R.string.feedback_title.asString(),
            style = DesignFonts.pjsMedium18(),
            color = DesignColors.black80,
            modifier = Modifier.align(Alignment.Center)
        )

        // Submit 按钮
        Text(
            text = R.string.feedback_submit.asString(),
            style = DesignFonts.pjsMedium16(),
            color = if (isSubmitEnabled) DesignColors.black100 else DesignColors.black30,
            modifier = Modifier
                .align(Alignment.CenterEnd)
                .debouncedClickable(enabled = isSubmitEnabled, onClick = onSubmitClick)
        )
    }
}

/**
 * Email 输入区域组件
 */
@Composable
private fun EmailInputSection(
    emailState: TextFieldState,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(10.dp)
    ) {
        // Email 标签
        Text(
            text = R.string.feedback_email_label.asString(),
            style = DesignFonts.pjsRegular16(),
            color = DesignColors.lightNtBk80
        )

        // Email 输入框
        CommonTextField(
            state = emailState,
            textStyle = DesignFonts.pjsRegular16(),
            fontColor = DesignColors.black80,
            hint = R.string.feedback_description_email.asString(),
            hintColor = DesignColors.black20,
            backgroundColor = DesignColors.lightNtBk05,
            backgroundShape = RoundedCornerShape(16.dp),
            horizontalPadding = 20.dp,
            lineLimits = TextFieldLineLimits.SingleLine
        )
    }
}

/**
 * Description 输入区域组件
 */
@Composable
private fun DescriptionInputSection(
    descriptionState: TextFieldState,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // Description 标签
        Text(
            text = R.string.feedback_description_label.asString(),
            style = DesignFonts.pjsRegular16(),
            color = DesignColors.black80
        )

        // Description 输入框
        CommonTextField(
            minHeight = 116.dp,
            state = descriptionState,
            textStyle = DesignFonts.pjsRegular16(),
            fontColor = DesignColors.black80,
            backgroundColor = DesignColors.lightNtBk05,
            backgroundShape = RoundedCornerShape(16.dp),
            horizontalPadding = 20.dp,
            lineLimits = TextFieldLineLimits.MultiLine(
                minHeightInLines = 4,
                maxHeightInLines = 15
            ),
            maxLength = 1000,
            hint = R.string.feedback_description_placeholder.asString(),
            hintColor = DesignColors.black20,
            verticalAlignment = Alignment.Top
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun FeedbackScreenPreview() {
    FeedbackScreen(
        onBackClick = {},
        onSubmitClick = { _, _ -> }
    )
}

@Preview(showBackground = true)
@Composable
private fun FeedbackTopBarPreview() {
    FeedbackTopBar(
        onBackClick = {},
        onSubmitClick = {},
        isSubmitEnabled = true
    )
}

@Preview(showBackground = true)
@Composable
private fun EmailInputSectionPreview() {
    val emailState = rememberTextFieldState(initialText = "<EMAIL>")
    EmailInputSection(
        emailState = emailState
    )
}

@Preview(showBackground = true)
@Composable
private fun DescriptionInputSectionPreview() {
    val descriptionState = rememberTextFieldState()
    DescriptionInputSection(
        descriptionState = descriptionState
    )
}
