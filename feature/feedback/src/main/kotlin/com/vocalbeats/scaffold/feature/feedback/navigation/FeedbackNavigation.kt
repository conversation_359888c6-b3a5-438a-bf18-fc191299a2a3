package com.vocalbeats.scaffold.feature.feedback.navigation

import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.vocalbeats.scaffold.feature.feedback.FeedbackScreen
import kotlinx.serialization.Serializable

/**
 * <AUTHOR>
 * @date 2025/8/11
 * @desc
 */
@Serializable data object FeedbackRoute

fun NavController.navigateToFeedback() {
    navigate(FeedbackRoute)
}

fun NavGraphBuilder.feedbackScreen(
    onNavBack: () -> Unit,
) {
    composable<FeedbackRoute> {
        FeedbackScreen(
            onNavBack= onNavBack,
        )
    }
}