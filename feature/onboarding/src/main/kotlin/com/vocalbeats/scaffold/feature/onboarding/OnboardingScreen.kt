package com.vocalbeats.scaffold.feature.onboarding

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.vocalbeats.designtoken.DesignColors
import com.vocalbeats.designtoken.DesignFonts
import com.vocalbeats.scaffold.core.common.compose.VerticalSpace
import com.vocalbeats.scaffold.core.common.compose.asString
import com.vocalbeats.scaffold.core.ui.PolicyTermsAndRestore
import com.vocalbeats.scaffold.core.common.R as CommonR

@Composable
fun OnboardingScreen(
    @DrawableRes imageRes: Int,
    @StringRes titleRes: Int,
    @StringRes tipRes: Int,
    onPrivacyPolicyClick: () -> Unit = {},
    onTermsOfServiceClick: () -> Unit = {},
    onRestoreClick: () -> Unit = {},
    onContinueClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    var animationStarted by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        animationStarted = true
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 20.dp)
            .systemBarsPadding()
    ) {
        val imageAlpha by animateFloatAsState(
            targetValue = if (animationStarted) 1f else 0f,
            animationSpec = tween(
                durationMillis = 500,
                delayMillis = 0
            ),
            label = "title_alpha"
        )

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .alpha(imageAlpha),
            contentAlignment = Alignment.Center
        ) {
            Image(
                modifier = Modifier.fillMaxWidth(),
                painter = painterResource(imageRes),
                contentDescription = "",
            )
        }

        val textOffsetY by animateFloatAsState(
            targetValue = if (animationStarted) 0f else 50f,
            animationSpec = tween(
                durationMillis = 800,
                delayMillis = 500
            ),
            label = "title_offset"
        )

        val textAlpha by animateFloatAsState(
            targetValue = if (animationStarted) 1f else 0f,
            animationSpec = tween(
                durationMillis = 800,
                delayMillis = 500
            ),
            label = "title_alpha"
        )

        val buttonAlpha by animateFloatAsState(
            targetValue = if (animationStarted) 1f else 0f,
            animationSpec = tween(
                durationMillis = 800,
                delayMillis = 1300
            ),
            label = "title_alpha"
        )


        Title(
            text = titleRes.asString(),
            offsetYLambda = { textOffsetY },
            alphaLambda = { textAlpha }
        )
        VerticalSpace(8.dp)
        Tips(
            text = tipRes.asString(),
            offsetYLambda = { textOffsetY },
            alphaLambda = { textAlpha }
        )
        VerticalSpace(24.dp)
        ContinueButton(
            text = CommonR.string.guidance_continue.asString(),
            alphaLambda = { buttonAlpha },
            onContinueClick = onContinueClick
        )
        VerticalSpace(12.dp)
        PolicyTermsAndRestore(
            modifier = Modifier.graphicsLayer {
                alpha = buttonAlpha
            },
            onPrivacyPolicyClick = onPrivacyPolicyClick,
            onTermsOfServiceClick = onTermsOfServiceClick,
            onRestoreClick = onRestoreClick
        )
        VerticalSpace(42.dp)
    }
}

@Composable
internal fun Tips(
    text: String,
    offsetYLambda: () -> Float,
    alphaLambda: () -> Float
) {
    Text(
        modifier = Modifier
            .fillMaxWidth()
            .graphicsLayer {
                translationY = offsetYLambda()
                alpha = alphaLambda()
            },
        text = text,
        style = DesignFonts.pjsRegular16(),
        color = DesignColors.lightNtBk60,
        textAlign = TextAlign.Center
    )
}

@Composable
internal fun Title(
    text: String,
    offsetYLambda: () -> Float,
    alphaLambda: () -> Float
) {
    Text(
        modifier = Modifier
            .fillMaxWidth()
            .graphicsLayer {
                translationY = offsetYLambda()
                alpha = alphaLambda()
            },
        text = text,
        style = DesignFonts.pjsBold26(),
        color = DesignColors.lightNtBk100,
        textAlign = TextAlign.Center
    )
}

@Composable
internal fun ContinueButton(
    text: String,
    alphaLambda: () -> Float,
    onContinueClick: () -> Unit
) {
    Button(
        onClick = onContinueClick,
        modifier = Modifier
            .fillMaxWidth()
            .height(56.dp)
            .graphicsLayer {
                alpha = alphaLambda()
            },
        colors = ButtonDefaults.buttonColors(
            containerColor = DesignColors.black100
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Text(
            text = text,
            style = DesignFonts.pjsMedium16(),
            color = DesignColors.white100
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun OnboardingScreenPreview() {
    OnboardingScreen(
        imageRes = R.drawable.onboarding_illustration,
        titleRes = CommonR.string.guidance_title,
        tipRes = CommonR.string.guidance_tips,
        onContinueClick = {}
    )
}