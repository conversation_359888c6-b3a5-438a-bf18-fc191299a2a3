package com.vocalbeats.scaffold.feature.settings

import android.R.attr.text
import android.content.ClipData
import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.ClipEntry
import androidx.compose.ui.platform.LocalClipboard
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.vocalbeats.designtoken.DesignColors
import com.vocalbeats.designtoken.DesignFonts
import com.vocalbeats.designtoken.DesignIcons
import com.vocalbeats.scaffold.core.common.R
import com.vocalbeats.scaffold.core.common.compose.VerticalSpace
import com.vocalbeats.scaffold.core.common.compose.asString
import com.vocalbeats.scaffold.core.common.compose.debouncedClickable
import com.vocalbeats.scaffold.core.designsystem.component.IconFontText
import com.yibasan.lizhifm.sdk.platformtools.Const
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @date 2025/8/12
 * @desc About Screen - 关于页面
 */
@Composable
fun AboutScreen(
    onNavBack: () -> Unit = {},
    onNavToPrivacyPolicy: () -> Unit = {},
    onNavToTermsOfUse: () -> Unit = {},
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(DesignColors.white100)
            .statusBarsPadding()
            .navigationBarsPadding()
    ) {
        // 顶部导航栏
        AboutTopBar(
            onBackClick = onNavBack,
            modifier = Modifier.fillMaxWidth()
        )

        // 内容区域
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            VerticalSpace(48.dp)

            // 应用图标
            AppIcon(
                modifier = Modifier.size(120.dp)
            )

            VerticalSpace(12.dp)

            // 应用名称
            Text(
                text = R.string.app_name.asString(),
                style = DesignFonts.pjsMedium18(),
                color = DesignColors.black80,
                textAlign = TextAlign.Center
            )

            VerticalSpace(6.dp)

            val version = "V ${Const.VersionName} (${Const.clientVersion})"

            // 版本信息
            Text(
                text = version,
                style = DesignFonts.pjsRegular14(),
                color = DesignColors.black60,
                textAlign = TextAlign.Center
            )

            VerticalSpace(41.dp)

            // 设置选项列表
            AboutOptionsCard(
                onPrivacyPolicyClick = onNavToPrivacyPolicy,
                onTermsOfUseClick = onNavToTermsOfUse,
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.weight(1f))

            // 底部版权信息
            CopyrightSection(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 10.dp)
            )
        }
    }
}

/**
 * 顶部导航栏组件
 */
@Composable
private fun AboutTopBar(
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .height(56.dp)
            .padding(horizontal = 20.dp),
        contentAlignment = Alignment.Center
    ) {
        // 返回按钮
        IconFontText(
            icon = DesignIcons.ArrowLeft,
            size = 24.dp,
            color = DesignColors.black80,
            onClick = onBackClick,
            modifier = Modifier.align(Alignment.CenterStart)
        )
    }
}

/**
 * 应用图标组件
 */
@Composable
private fun AppIcon(
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(28.dp))
            .background(DesignColors.black100),
        contentAlignment = Alignment.Center
    ) {
        // 这里应该放置应用图标，暂时用文字代替
        Text(
            text = R.string.app_name.asString(),
            style = DesignFonts.pjsMedium18(),
            color = DesignColors.white100,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 关于选项卡片组件
 */
@Composable
private fun AboutOptionsCard(
    onPrivacyPolicyClick: () -> Unit,
    onTermsOfUseClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(20.dp)
    ) {
        // Contact Us 选项
        val email = R.string.about_contact_email.asString()
        val localClipboard = LocalClipboard.current
        val context = LocalContext.current
        val scope = rememberCoroutineScope()
        AboutOptionItem(
            title = R.string.about_contact_us.asString(),
            subtitle = email,
            onClick = {
                // 复制邮箱然后Toast
                scope.launch {
                    localClipboard.setClipEntry(ClipEntry(ClipData.newPlainText(email, email)))
                    Toast.makeText(context, "copy", Toast.LENGTH_SHORT).show()
                }
            },
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(16.dp))
                .background(DesignColors.lightNtBk05)
        )

        // Privacy Policy 和 Terms of Use 选项
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(16.dp))
                .background(DesignColors.lightNtBk05)
        ) {
            AboutOptionItem(
                title = R.string.about_privacy_policy.asString(),
                onClick = onPrivacyPolicyClick,
                showArrow = true,
                modifier = Modifier.fillMaxWidth()
            )

            AboutOptionItem(
                title = R.string.about_terms_of_use.asString(),
                onClick = onTermsOfUseClick,
                showArrow = true,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 关于选项项组件
 */
@Composable
private fun AboutOptionItem(
    title: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    subtitle: String? = null,
    showArrow: Boolean = false,
) {
    Row(
        modifier = modifier
            .height(64.dp)
            .debouncedClickable(onClick = onClick)
            .padding(horizontal = 20.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 标题
        Text(
            text = title,
            style = DesignFonts.pjsMedium16(),
            color = DesignColors.black100,
            modifier = Modifier.weight(1f)
        )

        // 副标题或箭头
        if (subtitle != null) {
            Text(
                text = subtitle,
                style = DesignFonts.pjsRegular12(),
                color = DesignColors.black40
            )
        } else if (showArrow) {
            IconFontText(
                icon = DesignIcons.ArrowEnter,
                size = 16.dp,
                color = DesignColors.black40
            )
        }
    }
}

/**
 * 版权信息组件
 */
@Composable
private fun CopyrightSection(
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Text(
            text = R.string.about_copyright.asString(),
            style = DesignFonts.pjsRegular12(),
            color = DesignColors.black30,
            textAlign = TextAlign.Center
        )

        Text(
            text = R.string.about_typeface.asString(),
            style = DesignFonts.pjsRegular12(),
            color = DesignColors.black30,
            textAlign = TextAlign.Center
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun AboutTopBarPreview() {
    AboutTopBar(
        onBackClick = {}
    )
}

@Preview(showBackground = true)
@Composable
private fun AppIconPreview() {
    AppIcon()
}

@Preview(showBackground = true)
@Composable
private fun AboutOptionsCardPreview() {
    AboutOptionsCard(
        onPrivacyPolicyClick = {},
        onTermsOfUseClick = {}
    )
}

@Preview(showBackground = true)
@Composable
private fun CopyrightSectionPreview() {
    CopyrightSection()
}

@Preview(showBackground = true)
@Composable
private fun AboutScreenPreview() {
    AboutScreen()
}
