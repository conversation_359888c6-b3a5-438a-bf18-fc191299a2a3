package com.vocalbeats.scaffold.feature.settings.navigation

import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.vocalbeats.scaffold.feature.settings.AboutScreen
import com.vocalbeats.scaffold.feature.settings.SettingsScreen
import kotlinx.serialization.Serializable

/**
 * <AUTHOR>
 * @date 2025/8/11
 * @desc
 */
@Serializable data object SettingRoute
@Serializable data object AboutRoute

fun NavController.navigateToSettings() {
    navigate(SettingRoute)
}

fun NavController.navigateToAbout() {
    navigate(AboutRoute)
}

fun NavGraphBuilder.settingsScreen(
    onNavBack: () -> Unit,
    onNavToAbout: () -> Unit,
    onNavToFeedback: () -> Unit,
) {
    composable<SettingRoute> {
        SettingsScreen(
            onNavBack = onNavBack,
            onNavToAbout = onNavToAbout,
            onNavToFeedback = onNavToFeedback,
        )
    }
}

fun NavGraphBuilder.aboutScreen(
    onNavBack: () -> Unit,
    onNavToPrivacyPolicy: () -> Unit = {},
    onNavToTermsOfUse: () -> Unit = {},
) {
    composable<AboutRoute> {
        AboutScreen(
            onNavBack = onNavBack,
            onNavToPrivacyPolicy = onNavToPrivacyPolicy,
            onNavToTermsOfUse = onNavToTermsOfUse,
        )
    }
}