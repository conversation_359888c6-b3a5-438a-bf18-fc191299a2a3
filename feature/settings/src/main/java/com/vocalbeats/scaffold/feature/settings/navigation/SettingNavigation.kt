package com.vocalbeats.scaffold.feature.settings.navigation

import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.vocalbeats.scaffold.feature.settings.AboutScreen
import com.vocalbeats.scaffold.feature.settings.SettingsScreen
import kotlinx.serialization.Serializable

/**
 * <AUTHOR>
 * @date 2025/8/11
 * @desc
 */
@Serializable data object SettingRoute
@Serializable data object AboutRoute

fun NavController.navigateToSettings() {
    navigate(SettingRoute)
}

fun NavController.navigateToAbout() {
    navigate(AboutRoute)
}

fun NavGraphBuilder.settingsScreen(
    onBackClick: () -> Unit,
    onNavigateToAbout: () -> Unit,
    onNavigateToFeedback: () -> Unit,
) {
    composable<SettingRoute> {
        SettingsScreen(
            onBackClick = onBackClick,
            onNavigateToAbout = onNavigateToAbout,
            onNavigateToFeedback = onNavigateToFeedback,
        )
    }
}

fun NavGraphBuilder.aboutScreen(
    onBackClick: () -> Unit,
    onContactUsClick: () -> Unit = {},
    onPrivacyPolicyClick: () -> Unit = {},
    onTermsOfUseClick: () -> Unit = {},
) {
    composable<AboutRoute> {
        AboutScreen(
            onBackClick = onBackClick,
            onContactUsClick = onContactUsClick,
            onPrivacyPolicyClick = onPrivacyPolicyClick,
            onTermsOfUseClick = onTermsOfUseClick,
        )
    }
}