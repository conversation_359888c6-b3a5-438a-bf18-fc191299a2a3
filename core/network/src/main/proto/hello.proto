syntax = "proto3";

// 设置Android包名
option java_multiple_files = true;
option java_package = "com.vocalbeats.grpclib";
option java_outer_classname = "HelloWorldProto";
// 设置前缀
option objc_class_prefix = "VB_";
option swift_prefix = "VB_";

package hello;

// 定义Hello服务
service Greeter {

  // 一元调用：客户端发送一个请求，服务器返回一个响应
  rpc SayHello (HelloRequest) returns (HelloReply) {}

    // 客户端流：客户端发送多个请求，服务器返回一个响应
  rpc SayHelloClientStream (stream HelloRequest) returns (HelloReply) {}

    // 服务器流：客户端发送一个请求，服务器返回多个响应
    rpc SayHelloServerStream (HelloRequest) returns (stream HelloReply) {}

    // 双向流：客户端和服务器双向发送流
    rpc SayHelloBidiStream (stream HelloRequest) returns (stream HelloReply) {}

    // 一元调用：查询设备文件 URL
    rpc GetFileUrlsByDeviceId (FileUrlRequest) returns (FileUrlResponse) {}

    // 客户端流：批量上传设备 ID，返回汇总响应
    rpc GetFileUrlsClientStream (stream FileUrlRequest) returns (FileUrlResponse) {}

    // 服务器流：查询设备文件 URL，逐个返回
    rpc GetFileUrlsServerStream (FileUrlRequest) returns (stream FileUrlResponse) {}

    // 双向流：客户端和服务器双向处理设备 ID 和 URL
    rpc GetFileUrlsBidiStream (stream FileUrlRequest) returns (stream FileUrlResponse) {}
}

// 请求消息包含用户名
message HelloRequest {
  string name = 1;
}

// 响应消息包含问候语
message HelloReply {
  string message = 1;
}

// 查询文件URL请求消息
message FileUrlRequest {
  string device_id = 1;
}

// 文件URL记录
message FileUrlRecord {
  int64 id = 1;
  string url = 2;
  string summary = 3;
}

// 查询文件URL响应消息
message FileUrlResponse {
  repeated FileUrlRecord records = 1;
}