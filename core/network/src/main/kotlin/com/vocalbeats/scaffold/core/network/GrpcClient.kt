package com.vocalbeats.scaffold.core.network

import com.vocalbeats.grpclib.GreeterGrpcKt
import com.vocalbeats.grpclib.HelloRequest
import io.grpc.ManagedChannel
import io.grpc.ManagedChannelBuilder
import java.util.concurrent.TimeUnit

// TODO yangmulin 仅作案例，后续删除
class GrpcClient {
    private var channel: ManagedChannel? = null
    private var greeterCoroutineStub: GreeterGrpcKt.GreeterCoroutineStub? = null

    fun connect(host: String, port: Int) {
        channel = ManagedChannelBuilder.forAddress(host, port)
            .usePlaintext()
            .build()

        greeterCoroutineStub = GreeterGrpcKt.GreeterCoroutineStub(channel!!)
    }

    suspend fun sayHello(name: String): String {
        val request = HelloRequest.newBuilder().setName(name).build()
        val reply = greeterCoroutineStub?.sayHello(request)
        return reply?.message ?: ""
    }

    fun shutdown() {
        channel?.shutdown()
        try {
            channel?.awaitTermination(5, TimeUnit.SECONDS)
        } catch (e: InterruptedException) {
            Thread.currentThread().interrupt()
        }
    }
}