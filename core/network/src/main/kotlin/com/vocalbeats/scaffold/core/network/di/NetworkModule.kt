package com.vocalbeats.scaffold.core.network.di

import com.vocalbeats.grpclib.commom.CommonServiceGrpcKt
import com.vocalbeats.scaffold.core.network.GrpcClient
import com.vocalbeats.scaffold.core.network.model.CommonServiceClient
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {


    @Provides
    fun provideCommonServiceStub(provider: GrpcClient): CommonServiceGrpcKt.CommonServiceCoroutineStub{
        return provider.provideCommonCoroutineStub()
    }

    @Provides
    fun provideCommonServiceClient(stub: CommonServiceGrpcKt.CommonServiceCoroutineStub): CommonServiceClient {
        return CommonServiceClient(stub)
    }
}
