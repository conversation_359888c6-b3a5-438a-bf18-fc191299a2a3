plugins {
    alias(libs.plugins.vocalbeats.android.library)
    alias(libs.plugins.vocalbeats.hilt)
    alias(libs.plugins.protobuf)
}

android {
    namespace = "${rootProject.ext["packageName"]}.core.network"

    sourceSets {
        named("main") {
            java {
                srcDirs("build/generated/source/proto/main/java")
            }
            kotlin {
                srcDirs("build/generated/source/proto/main/kotlin")
            }
        }
    }
}

protobuf {
    protoc {
        artifact = libs.protobuf.protoc.get().toString()
    }

    plugins {
        create("grpc") {
            artifact = libs.gen.grpc.java.get().toString()
        }
        create("grpckt") {
            artifact = libs.gen.grpc.kotlin.get().toString()
        }
    }

    generateProtoTasks {
        all().forEach {
            it.plugins {
                create("grpc") {
                    option("lite")
                }
                create("grpckt") {
                    option("lite")
                }
            }
            it.builtins {
                create("java") {
                    option("lite")
                }
                create("kotlin") {
                    option("lite")
                }
            }
        }
    }
}

dependencies {
    api(projects.core.common)
    api(projects.core.model)

    // gRPC 核心依赖
    implementation(libs.grpc.okhttp)
    implementation(libs.grpc.protobuf.lite)
    implementation(libs.grpc.stub)
    implementation(libs.protobuf.kotlin.lite)
    implementation(libs.javax.annotation)
    implementation(libs.grpc.kotlin.stub)
    implementation(libs.protobuf.javalite)
}