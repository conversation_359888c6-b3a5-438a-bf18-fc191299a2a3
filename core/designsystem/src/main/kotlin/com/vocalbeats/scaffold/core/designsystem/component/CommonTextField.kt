package com.vocalbeats.scaffold.core.designsystem.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.background
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.input.*
import androidx.compose.foundation.text.input.TextFieldLineLimits.MultiLine
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.BiasAlignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.semantics.SemanticsPropertyReceiver
import androidx.compose.ui.semantics.maxTextLength
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import com.vocalbeats.designtoken.DesignColors
import com.vocalbeats.designtoken.DesignIcons
import com.vocalbeats.scaffold.core.common.compose.getBias

/**
 * <AUTHOR>
 * @date 2024/8/7
 * @desc 通用文本输入框组件
 *
 * @param modifier 修饰符，用于设置组件的外观和布局属性
 *                  Modifier to set the appearance and layout properties of the component
 * @param state 文本输入框的状态，包含当前文本和光标位置等信息
 *                  State of the text field, containing current text and cursor position
 * @param fontSize 字体大小，以sp为单位
 *                  Font size in sp units
 * @param fontColor 字体颜色，用于设置文本的颜色
 *                  Font color to set the color of the text
 * @param textStyle 文本样式，用于设置文本的字体、粗细等属性
 *                  Text style to set the font, weight, etc of the text
 * @param maxLength 最大长度，限制输入文本的字符数
 *                  Maximum length to limit the number of characters in the input text
 * @param lineLimits 行限制，定义文本框是单行还是多行
 *                  Line limits to define whether the text field is single-line or multi-line
 * @param hint 提示文本，当文本框为空时显示
 *                  Hint text displayed when the text field is empty
 * @param hintColor 提示文本颜色，用于设置提示文本的颜色
 *                  Hint text color to set the color of the hint text
 * @param verticalAlignment 内容对齐方式，定义文本在文本框中的垂直对齐方式
 *                  Content alignment to define the vertical alignment of the text within the text field
 * @param backgroundColor 背景颜色，用于设置文本框的背景颜色
 *                  set the background color of the text field
 * @param backgroundShape 背景颜色，用于设置文本框的背景形状
 *                  set the background shape of the text field
 * @param padding 边距，用于设置文本框内容的内边距
 *                  set padding of the text field content
 * @param enabled 是否启用，定义文本框是否可以交互
 *                  Whether the text field is enabled, defining if it can be interacted with
 * @param enabledClearText 是否启用清除文本按钮，显示一个按钮以清除文本框内容
 *                  Whether the clear text button is enabled, showing a button to clear the text field content
 * @param readOnly 是否只读，定义文本框是否只能读取而不能编辑
 *                  Whether the text field is read-only, defining if it can only be read and not edited
 * @param leading 前置内容，显示在文本框前面的自定义内容
 *                  Leading content to display custom content before the text field
 * @param trailing 后置内容，显示在文本框后面的自定义内容
 *                  Trailing content to display custom content after the text field
 * @param keyboardOptions 键盘选项，用于设置键盘的行为和外观
 *                  Keyboard options to set the behavior and appearance of the keyboard
 * @param onKeyboardAction 键盘操作处理器，处理键盘操作事件
 *                  Keyboard action handler to handle keyboard action events
 * @param onTextLayout 文本布局回调，处理文本布局结果
 *                  Text layout callback to handle the text layout result
 * @param interactionSource 交互源，用于处理交互事件
 *                  Interaction source to handle interaction events
 * @param cursorBrush 光标画笔，用于设置光标的颜色和样式
 *                  Cursor brush to set the color and style of the cursor
 * @param inputTransformation 输入转换，用于在文本输入时进行转换
 *                  Input transformation to transform the text during input
 * @param outputTransformation 输出转换，用于在文本输出时进行转换
 *                  Output transformation to transform the text during output
 * @param decorator 文本框装饰器，用于自定义文本框的外观
 *                  Text field decorator to customize the appearance of the text field
 * @param scrollState 滚动状态，用于处理文本框的滚动行为
 *                  Scroll state to handle the scrolling behavior of the text field
 */
@Composable
fun CommonTextField(
    modifier: Modifier = Modifier,
    state: TextFieldState,
    textStyle: TextStyle = TextStyle.Default,
    fontSize: TextUnit = textStyle.fontSize,
    fontColor: Color = Color.Black.copy(alpha = 0.8f),
    minHeight: Dp = 50.dp,
    maxLength: Int? = null,
    reachMaxLengthCallback: (() -> Unit)? = null,
    lineLimits: TextFieldLineLimits = TextFieldLineLimits.SingleLine,
    hint: String? = null,
    hintColor: Color = Color.Black.copy(alpha = 0.2f),
    verticalAlignment: Alignment.Vertical = Alignment.Top,
    horizontalAlignment: Alignment.Horizontal = Alignment.Start,
    backgroundColor: Color = Color.Black.copy(alpha = 0.05f),
    backgroundShape: Shape = RoundedCornerShape(4.dp),
    padding: TextFieldPadding = TextFieldPadding(),
    enabled: Boolean = true,
    enabledClearText: Boolean = false,
    readOnly: Boolean = false,
    leading: (@Composable RowScope.() -> Unit)? = null,
    trailing: (@Composable RowScope.() -> Unit)? = null,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    onKeyboardAction: KeyboardActionHandler? = null,
    onTextLayout: (Density.(getResult: () -> TextLayoutResult?) -> Unit)? = null,
    interactionSource: MutableInteractionSource? = null,
    cursorBrush: Brush = SolidColor(Color.Black),
    inputTransformation: InputTransformation? = null,
    outputTransformation: OutputTransformation? = null,
    decorator: TextFieldDecorator? = null,
    scrollState: ScrollState = rememberScrollState(),
) {
    val inputTrans = maxLength?.let { max ->
        InputFilterMaxLength(max, reachMaxLengthCallback).let { maxLengthFilter ->
            inputTransformation?.then(maxLengthFilter) ?: maxLengthFilter
        }
    } ?: inputTransformation
    val isTextEmpty by remember { derivedStateOf { state.text.isEmpty() }  }
    BasicTextField(
        state = state,
        modifier = modifier,
        enabled = enabled,
        readOnly = readOnly,
        inputTransformation = inputTrans,
        textStyle = textStyle.copy(color = fontColor, fontSize = fontSize),
        keyboardOptions = keyboardOptions,
        onKeyboardAction = onKeyboardAction,
        lineLimits = lineLimits,
        onTextLayout = onTextLayout,
        interactionSource = interactionSource,
        cursorBrush = cursorBrush,
        outputTransformation = outputTransformation,
        scrollState = scrollState,
        decorator = decorator ?: TextFieldDecorator { innerTextField ->
            CommonTextFieldContent(
                innerTextField = innerTextField,
                valueIsEmpty = { isTextEmpty },
                fontSize = fontSize,
                textStyle = textStyle,
                minHeight = minHeight,
                maxLines = if (lineLimits is MultiLine) lineLimits.maxHeightInLines else 1,
                hint = hint,
                hintColor = hintColor,
                verticalAlignment = verticalAlignment,
                horizontalAlignment = horizontalAlignment,
                backgroundColor = backgroundColor,
                backgroundShape = backgroundShape,
                padding = padding,
                enabledClearText = enabledClearText,
                onClearValue = { state.clearText() },
                leading = leading,
                trailing = trailing,
            )
        }
    )
}

data class TextFieldPadding(
    val start: Dp = 20.dp,
    val top: Dp = 0.dp,
    val end: Dp = 20.dp,
    val bottom: Dp = 0.dp,
) {
    constructor(horizontal: Dp = 20.dp, vertical: Dp = 0.dp) : this(horizontal, vertical, horizontal, vertical)
    constructor(all: Dp) : this(all, all, all, all)
}

fun TextFieldState.updateText(text:String) = edit {
    replace(0, length, text)
}

//放置背景等布局,并放置基础输入框
@Composable
private inline fun CommonTextFieldContent(
    innerTextField: @Composable () -> Unit,
    valueIsEmpty: () -> Boolean,
    fontSize: TextUnit,
    textStyle: TextStyle,
    minHeight: Dp = 50.dp,
    maxLines: Int,
    hint: String?,
    hintColor: Color,
    verticalAlignment: Alignment.Vertical,
    horizontalAlignment: Alignment.Horizontal,
    backgroundColor: Color,
    backgroundShape: Shape,
    padding: TextFieldPadding,
    enabledClearText: Boolean,
    noinline onClearValue: () -> Unit,
    noinline leading: (@Composable RowScope.() -> Unit)?,
    noinline trailing: (@Composable RowScope.() -> Unit)?,
) {
    Row(
        Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .heightIn(min = minHeight)
            .background(backgroundColor, backgroundShape)
            .padding(
                start = padding.start,
                end = padding.end,
                top = padding.top,
                bottom = padding.bottom
            ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (leading != null) {
            leading()
            Spacer(Modifier.width(padding.start))
        }
        Box(
            modifier = Modifier.weight(1f),
            contentAlignment = BiasAlignment(
                horizontalBias = horizontalAlignment.getBias(),
                verticalBias = verticalAlignment.getBias()
            )
        ) {
            if (valueIsEmpty() && !hint.isNullOrEmpty()) {
                Text(
                    text = hint,
                    style = textStyle.copy(color = hintColor, fontSize = fontSize),
                    maxLines = maxLines,
                )
            }
            innerTextField()
        }
        // 如果启用尾部的清空 Text 按钮，则 trailing 会失效
        if (enabledClearText) {
            AnimatedVisibility(visible = !valueIsEmpty.invoke()) {
                Row {
                    Spacer(Modifier.width(padding.end))
                    IconFontText(
                        icon = DesignIcons.Delete_line, // todo 替换成实际图标
                        size = 18.dp,
                        color = DesignColors.black100,
                        onClick = onClearValue
                    )
                }
            }
        } else {
            if (trailing != null) {
                Spacer(Modifier.width(padding.end))
                trailing()
            }
        }
    }
}

data class InputFilterMaxLength(
    private val maxLength: Int,
    private val reachMaxLengthCallback: (() -> Unit)? = null
) : InputTransformation {

    init {
        require(maxLength >= 0) { "maxLength must be at least zero, was $maxLength" }
    }

    override fun SemanticsPropertyReceiver.applySemantics() {
        maxTextLength = maxLength
    }

    override fun TextFieldBuffer.transformInput() {
        // 总计输入内容没有超出长度限制
        if (length <= maxLength) return
        // 即使超出长度也允许删减字符
        if (originalText.length > length) return

        // 输入内容超出了长度限制, 这里要分两种情况：
        // 1. 直接输入的，则返回原数据即可
        // 2. 粘贴后会导致长度超出，此时可能还可以输入部分字符，所以需要判断后截断输入

        val oldStart = originalSelection.start
        val oldEnd = originalSelection.end

        // 计算实际的旧字符数，以总字符数-被光标框选的长度（因为这部分会被替换）
        val oldCount = (originalText.length - (oldEnd - oldStart))
        val newCount = length

        // 计算这次新增了几个字符
        val inputCharCount = newCount - oldCount
        val allowCount = maxLength - oldCount
        // 允许再输入字符已经为空，则直接返回原数据
        if (allowCount <= 0) {
            revertAllChanges()
            reachMaxLengthCallback?.invoke()
            return
        } else {
            try {
                // 同时粘贴了多个字符内容
                if (inputCharCount > 1) {
                    // 截取应该新增的字符部分
                    val newChar = asCharSequence().substring(oldStart, oldStart + allowCount)

                    // 从光标起始位置开始插入新增字符（前后补全旧字符）
                    val newText = buildString {
                        append(originalText, 0, oldStart)
                        append(newChar)
                        append(originalText, oldEnd, originalText.length)
                    }
                    replace(0, length, newText)
                    selection = TextRange(oldStart + newChar.length)
                    // 检查是否达到最大长度
                    if (newText.length == maxLength) {
                        reachMaxLengthCallback?.invoke()
                    }
                }
            } catch (e: Exception) {
                revertAllChanges()
            }
        }
    }

    override fun toString(): String {
        return "InputTransformation.maxLength($maxLength)"
    }
}

/**
 * Preview
 */
@Composable
@Preview(backgroundColor = 0xFFFFFFFF, showBackground = true)
private fun PreviewCommonTextField() {
    val defaultText = "defaultText"
    val hintText = "hintText"
    val textFieldState = rememberTextFieldState(initialText = defaultText)// 默认文案
    //自动弹出键盘
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusRequester = remember { FocusRequester() }
    LaunchedEffect(key1 = Unit, block = {
        focusRequester.requestFocus()
        keyboardController?.show()
    })
    CommonTextField(
        modifier = Modifier
            .fillMaxWidth()
            .padding(all = 40.dp)
            .fillMaxWidth()
            .height(50.dp)
            .focusRequester(focusRequester),
        state = textFieldState,
        // textStyle = TextStyles.bodyLarge().copy(textAlign = TextAlign.Center),
        // horizontalAlignment = Alignment.CenterHorizontally,
        hint = hintText, //底层 hint
        maxLength = 50,  //最大长度
        enabledClearText = true, //是否启用一键删除
    )
}