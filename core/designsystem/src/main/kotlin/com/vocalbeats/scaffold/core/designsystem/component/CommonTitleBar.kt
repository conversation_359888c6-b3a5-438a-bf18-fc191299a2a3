package com.vocalbeats.scaffold.core.designsystem.component

import androidx.annotation.StringRes
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.vocalbeats.designtoken.DesignColors
import com.vocalbeats.designtoken.DesignFonts
import com.vocalbeats.designtoken.DesignIcons
import com.vocalbeats.scaffold.core.common.R
import com.vocalbeats.scaffold.core.common.compose.asString
import com.vocalbeats.scaffold.core.common.compose.debouncedClickable

/**
 * <AUTHOR>
 * @date 2025/8/14
 * @desc 通用顶部标题栏
 */
@Composable
fun CommonTitleBar(
    @StringRes titleRes: Int?,
    onNavBack: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val iconSize = 40.dp
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(56.dp)
            .padding(horizontal = 12.dp)
    ) {
        // 返回按钮
        Box(
            modifier = Modifier
                .size(iconSize)
                .align(Alignment.CenterStart)
                .debouncedClickable(onClick = onNavBack),
            contentAlignment = Alignment.Center
        ) {
            IconFontText(
                icon = DesignIcons.ArrowLeft,
                size = 24.dp,
                color = DesignColors.black80,
            )
        }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = iconSize)
                .align(Alignment.Center),
            contentAlignment = Alignment.Center
        ) {
            titleRes?.asString()?.let { title ->
                // 标题
                Text(
                    text = title,
                    style = DesignFonts.pjsMedium18(),
                    color = DesignColors.black80,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier
                        .align(Alignment.Center)
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun CommonTitleBarPreview() {
    CommonTitleBar(
        titleRes = R.string.settings_title,
        onNavBack = {}
    )
}

@Preview(showBackground = true)
@Composable
private fun CommonTitleBarLongTitlePreview() {
    CommonTitleBar(
        titleRes = R.string.feedback_description_placeholder,
        onNavBack = {}
    )
}

@Preview(showBackground = true)
@Composable
private fun CommonTitleBarNoTitlePreview() {
    CommonTitleBar(
        titleRes = null,
        onNavBack = {}
    )
}