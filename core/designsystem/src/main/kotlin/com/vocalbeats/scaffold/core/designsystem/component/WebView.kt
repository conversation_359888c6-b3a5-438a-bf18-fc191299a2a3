package com.vocalbeats.scaffold.core.designsystem.component

import android.webkit.WebView
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView

@Composable
fun WebView(
    url: String,
    headers: Map<String, String> = emptyMap(),
    onCreated: () -> Unit = {},
    onRelease: () -> Unit = {},
    modifier: Modifier = Modifier,
){
    AndroidView(
        factory = { context ->
            WebView(context).apply {
                onCreated()
                loadUrl(url, headers)
            }
        },
        modifier = modifier,
        onRelease = {
            onRelease()
        },
    )
}