package com.vocalbeats.scaffold.core.designsystem.component

import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.PlatformTextStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.vocalbeats.scaffold.core.common.compose.debouncedClickable
import com.vocalbeats.scaffold.core.common.compose.textSp

/**
 * <AUTHOR>
 * @date 2024/1/29
 * @desc Common Compose components related to Text
 */
internal val fontIcon = FontFamily(Font(resId = com.vocalbeats.designtoken.R.font.rco))

@Composable
fun getIconFontTextStyle(): TextStyle {
    return TextStyle.Default.copy(
        fontFamily = fontIcon,
        fontStyle = FontStyle.Normal,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    )
}

@Composable
fun IconFontText(
    icon: String,
    size: Dp = 24.dp,
    color: Color = Color.White,
    modifier: Modifier = Modifier,
) {
    Text(
        modifier = modifier,
        text = icon,
        color = color,
        textAlign = TextAlign.Center,
        fontSize = size.textSp(),
        style = getIconFontTextStyle()
    )
}

@Composable
fun IconFontText(
    icon: String,
    size: Dp = 24.dp,
    color: Color = Color.White,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Text(
        modifier = modifier.debouncedClickable(onClick = onClick),
        text = icon,
        color = color,
        textAlign = TextAlign.Center,
        fontSize = size.textSp(),
        style = getIconFontTextStyle()
    )
}
