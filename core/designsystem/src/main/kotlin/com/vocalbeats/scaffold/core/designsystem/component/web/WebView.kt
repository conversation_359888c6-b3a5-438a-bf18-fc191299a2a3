package com.vocalbeats.scaffold.core.designsystem.component.web

import android.webkit.WebView
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.viewinterop.AndroidView

@Composable
fun WebView(
    state: WebViewState,
    modifier: Modifier = Modifier,
    onCreated: () -> Unit = {},
    onRelease: () -> Unit = {},
){
    AndroidView(
        factory = { context ->
            WebView(context).apply {
                onCreated()
            }.also {
                val callback = WebWebCallbackImpl(it)
                state.webViewCallback = callback
                state.url?.let { url ->
                    callback.loadUrl(url, state.headers)
                }
            }
        },
        modifier = modifier,
        onRelease = {
            onRelease()
        },
    )
}

@Preview
@Composable
private fun WebViewPreview() {
    val webViewState = remember {
        WebViewState().apply {
            url = "https://baidu.com"
        }
    }
    WebView(
        state = webViewState,
        modifier = Modifier.fillMaxSize(),
    )
}