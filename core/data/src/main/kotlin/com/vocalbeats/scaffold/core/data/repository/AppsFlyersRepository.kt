package com.vocalbeats.scaffold.core.data.repository

import com.vocalbeats.scaffold.core.model.AppsFlyersData
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

/**
 * yangmulin 要确保单一实例
 */
class AppsFlyersRepository @Inject constructor() {
    private val appsFlyerData = MutableStateFlow<AppsFlyersData?>(null)


    fun getAppsFlyerDataFlow(): StateFlow<AppsFlyersData?> {
        return appsFlyerData
    }

    fun onAppsFlyerCallBack(data: AppsFlyersData){
        appsFlyerData.tryEmit(data)
    }
}