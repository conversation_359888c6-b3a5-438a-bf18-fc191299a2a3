package com.vocalbeats.scaffold.core.data.di

import com.vocalbeats.scaffold.core.data.util.ConnectivityManagerNetworkMonitor
import com.vocalbeats.scaffold.core.data.util.NetworkMonitor
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

/**
 * <AUTHOR>
 * @date 2025/8/1
 * @desc
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class DataModule {

    @Binds
    internal abstract fun bindsNetworkMonitor(
        networkMonitor: ConnectivityManagerNetworkMonitor,
    ): NetworkMonitor
}