@file:Suppress("unused")

package com.vocalbeats.scaffold.core.common.ktx

import android.app.Application
import android.content.pm.ApplicationInfo
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ProcessLifecycleOwner

lateinit var application: Application

fun Application.initApplication(buildType: String) {
    application = this
    appBuildType = buildType
}

val isAppInForeground: Boolean
get() =
    ProcessLifecycleOwner.get().lifecycle.currentState.isAtLeast(Lifecycle.State.STARTED)

val isDebug: Boolean by lazy {
    application.packageManager.getApplicationInfo(
        packageName,
        0
    ).flags and ApplicationInfo.FLAG_DEBUGGABLE != 0
}

lateinit var appBuildType: String

val isReleaseLogBuildType: Boolean get() = appBuildType == "releaseLog"

inline val packageName: String get() = application.packageName

val appName: String by lazy {
    application.applicationInfo.loadLabel(application.packageManager).toString()
}

