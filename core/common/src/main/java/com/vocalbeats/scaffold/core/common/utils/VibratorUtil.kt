package com.vocalbeats.scaffold.core.common.utils

import android.content.Context
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager

object VibratorUtil {

    private const val DEFAULT_MS = 5L
    private const val DEFAULT_AMPLITUDE = 100

    private var vibrator: Vibrator? = null

    /**
     * 默认的振动效果
     */
    fun vibrator(context: Context) = vibrator(context, DEFAULT_MS, DEFAULT_AMPLITUDE)

    fun vibrator(
        context: Context,
        time: Long = DEFAULT_MS,
        amplitude: Int? = 100
    ) {
        runCatching {
            if (VERSION.SDK_INT >= VERSION_CODES.O) {
                val vibe = VibrationEffect.createOneShot(time, amplitude ?: DEFAULT_AMPLITUDE)
                getVibrator(context)?.vibrate(vibe)
            } else {
                @Suppress("DEPRECATION")
                getVibrator(context)?.vibrate(time)
            }
        }
    }

    fun vibrator(context: Context, vibe: VibrationEffect, from: String? = null) {
        runCatching {
            if (VERSION.SDK_INT >= VERSION_CODES.O) {
                getVibrator(context)?.vibrate(vibe)
            } else {
                @Suppress("DEPRECATION")
                getVibrator(context)?.vibrate(DEFAULT_MS)
            }
        }
    }

    private fun getVibrator(context: Context): Vibrator? {
        if (vibrator == null) {
            vibrator = if (VERSION.SDK_INT >= VERSION_CODES.S) {
                val vibratorManager =
                    context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
                vibratorManager.defaultVibrator
            } else {
                @Suppress("DEPRECATION")
                context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
            }
        }
        return vibrator
    }
}