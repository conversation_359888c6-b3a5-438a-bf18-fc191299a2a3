@file:Suppress("unused")

package com.vocalbeats.scaffold.core.common.ktx

import android.content.Context
import androidx.annotation.ColorInt
import androidx.core.content.ContextCompat

fun Int.asStringCtx(context: Context): String = ContextCompat.getString(context,this)

@ColorInt
fun Int.asColorCtx(context: Context) = ContextCompat.getColor(context, this)

fun Int.asDrawableCtx(context: Context) = ContextCompat.getDrawable(context, this)

fun Int.asDimensionCtx(context: Context) = context.resources.getDimension(this)