package com.vocalbeats.scaffold.core.database.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.vocalbeats.scaffold.core.model.ExampleData

/**
 * <AUTHOR>
 * @date 2025/8/1
 * @desc
 */
@Entity(
    tableName = "example",
)
data class ExampleEntity(
    @PrimaryKey
    val id: Long,
    val name: String,
)

fun ExampleEntity.asExternalModel() = ExampleData(
    id = id,
    name = name,
)