package com.vocalbeats.scaffold.core.web.functions

import com.yibasan.lizhifm.sdk.webview.jswebview.LJavaScriptWebView
import com.yibasan.lizhifm.sdk.webview.jswebview.bean.JsCallbackDetail
import kotlin.jvm.java

object JSFunctionManager {
    private val FUNCTIONS = HashMap<String, Class<out JSFunction?>>()

    init {
        FUNCTIONS[GetTokenFunction.METHOD_NAME] = GetTokenFunction::class.java
        FUNCTIONS[GetAppInfoFunction.METHOD_NAME] = GetAppInfoFunction::class.java
        FUNCTIONS[CloseWebViewFunction.METHOD_NAME] = CloseWebViewFunction::class.java
        FUNCTIONS[GetFeedbackDotVisibleFunction.METHOD_NAME] = GetFeedbackDotVisibleFunction::class.java
        FUNCTIONS[HideFeedbackDotFunction.METHOD_NAME] = HideFeedbackDotFunction::class.java
        FUNCTIONS[StartRecordFunction.METHOD_NAME] = StartRecordFunction::class.java
        FUNCTIONS[StopRecordFunction.METHOD_NAME] = StopRecordFunction::class.java
        FUNCTIONS[CancelRecordVoiceFunction.METHOD_NAME] = CancelRecordVoiceFunction::class.java
        FUNCTIONS[ExecuteActionFunction.METHOD_NAME] = ExecuteActionFunction::class.java
        FUNCTIONS[ShareCampaignFunction.METHOD_NAME] = ShareCampaignFunction::class.java
        FUNCTIONS[UploadFileFunction.METHOD_NAME] = UploadFileFunction::class.java
        FUNCTIONS[CancelUploadFileFunction.METHOD_NAME] = CancelUploadFileFunction::class.java
        FUNCTIONS[GetCampaignShareLinkFunction.METHOD_NAME] = GetCampaignShareLinkFunction::class.java
        FUNCTIONS[GetUserIdFunction.METHOD_NAME] = GetUserIdFunction::class.java
        FUNCTIONS[OpenStoreFunction.METHOD_NAME] = OpenStoreFunction::class.java
        FUNCTIONS[OpenWebBySystemFunction.METHOD_NAME] = OpenWebBySystemFunction::class.java
        FUNCTIONS[SetTitleStyleFunction.METHOD_NAME] = SetTitleStyleFunction::class.java
        FUNCTIONS[MethodSupportedFunction.METHOD_NAME] = MethodSupportedFunction::class.java
    }
    fun queryExistFunction(functionName:String?):Boolean{
        if (functionName.isNullOrEmpty()) {
            return false
        }
        return FUNCTIONS.containsKey(functionName)
    }

    fun invoke(activity: BaseActivity, webView: LJavaScriptWebView, data: JSFunctionData): JsCallbackDetail? {
        val callback = if (FUNCTIONS.containsKey(data.method)) {
            FUNCTIONS[data.method]?.newInstance()?.invoke(activity, webView, data)
        } else {
            null
        }
        return callback
    }
}