package com.vocalbeats.scaffold.core.web.functions

import androidx.activity.ComponentActivity
import com.vocalbeats.scaffold.core.common.ktx.logInfo
import com.yibasan.lizhifm.sdk.webview.jswebview.LJavaScriptWebView
import com.yibasan.lizhifm.sdk.webview.jswebview.bean.JsCallbackDetail

/**
 * <AUTHOR>
 * @date 2022/7/27
 * @desc
 */
internal class CloseWebViewFunction : JSFunction() {

    companion object {
        const val METHOD_NAME = "closeWebView"
    }

    override fun invoke(
        activity: ComponentActivity,
        webView: LJavaScriptWebView,
        data: JSFunctionData
    ): JsCallbackDetail? {
        logInfo(TAG, "CloseWebViewFunction")
        activity.onBackPressedDispatcher.onBackPressed()
        return getSuccessCallback(data)
    }
}