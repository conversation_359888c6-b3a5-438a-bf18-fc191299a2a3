package com.interfun.buz.common.web

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import androidx.annotation.ColorInt
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.isDebug
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.registerInterfaceBridge
import com.interfun.buz.base.ktx.startActivity
import com.interfun.buz.base.ktx.unregisterInterfaceBridge
import com.interfun.buz.common.constants.PATH_COMMON_ACTIVITY_WEB_VIEW
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.manager.router.converter.WebViewRouterArgs
import com.interfun.buz.common.web.interfaces.ITitleBarStyleControl
import com.yibasan.lizhifm.sdk.webview.WebViewManagerKt
import com.yibasan.lizhifm.sdk.webview.jswebview.jsBridgeConfig
import dagger.hilt.android.AndroidEntryPoint

/**
 * <AUTHOR>
 * @date 2022/7/26
 * @desc https://lizhi2021.feishu.cn/wiki/wikcn0PDTLgvJSqOxgYvjHMt7Lg
 */
@Route(path = PATH_COMMON_ACTIVITY_WEB_VIEW)
@AndroidEntryPoint
class WebViewActivity : BaseWebViewActivity(), ITitleBarStyleControl {

    companion object {
        const val TAG = "WebViewActivity"

        fun start(context: Context = appContext, url: String) {
            logDebug(TAG,"start")
            context.startActivity<WebViewActivity>(RouterParamKey.Common.KEY_URL to url)
        }

        fun start(context: Context = appContext, args: WebViewRouterArgs) {
            logDebug(TAG,"start")
            context.startActivity<WebViewActivity>(
                RouterParamKey.Common.KEY_URL to args.url,
                RouterParamKey.WebView.KEY_SHOW_NATIVE_TITLE_BAR to args.showNativeTitleBar,
                RouterParamKey.WebView.KEY_FORCE_TITLE to args.forceTitle,
                RouterParamKey.WebView.KEY_PLACEHOLDER_TITLE to args.placeholderTitle,
            )
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        this.registerInterfaceBridge(ITitleBarStyleControl::class.java, this)
    }

    override fun changeTitleBg(@ColorInt color: Int) {
        binding.headerView.background = ColorDrawable(color)
    }

    override fun changeTitleFontColor(@ColorInt color: Int) {
        binding.headerView.setTitleColor(color)
    }

    override fun changeTitleRetArrowColor(@ColorInt color: Int) {
        binding.headerView.setLeftIconColor(color)
    }

    override fun onDestroy() {
        super.onDestroy()
        this.unregisterInterfaceBridge(ITitleBarStyleControl::class.java)
    }
}