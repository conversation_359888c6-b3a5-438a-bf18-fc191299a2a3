package com.vocalbeats.scaffold.core.web

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.AttributeSet
import android.webkit.ValueCallback
import android.webkit.WebSettings
import androidx.core.content.ContextCompat.startActivity
import com.vocalbeats.scaffold.core.common.ktx.logInfo
import com.yibasan.lizhifm.sdk.webview.BuildConfig
import com.yibasan.lizhifm.sdk.webview.LConsoleMessage
import com.yibasan.lizhifm.sdk.webview.LFileChooserParams
import com.yibasan.lizhifm.sdk.webview.LJsPromptResult
import com.yibasan.lizhifm.sdk.webview.LJsResult
import com.yibasan.lizhifm.sdk.webview.LWebChromeClient
import com.yibasan.lizhifm.sdk.webview.LWebResourceRequest
import com.yibasan.lizhifm.sdk.webview.LWebSettings
import com.yibasan.lizhifm.sdk.webview.LWebView
import com.yibasan.lizhifm.sdk.webview.LWebViewClient
import com.yibasan.lizhifm.sdk.webview.jswebview.LJavaScriptWebView

interface WebChromeClientCallBack {
    fun onReceivedTitle(view: LWebView?, title: String?)
}

/**
 * <AUTHOR>
 * @date 2022/8/10
 * @desc
 */
class JSWebView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : LJavaScriptWebView(context, attrs) {

    companion object {
        const val TAG = "WebView JsBridge"
    }

    var webChromeClientCallBack: WebChromeClientCallBack? = null

    var fileChooserCallback: ((lc: ValueCallback<Array<Uri>>?, params: LFileChooserParams?) -> Unit)? =
        null

    init {
        initWebViewSetting()
        initWebChromeClient()
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initWebViewSetting() {
        /*WebView漏洞，移除该接口可避免恶意的代码注入*/
        if (BuildConfig.DEBUG) {
            setWebContentsDebuggingEnabled(true)
        }else{
            setWebContentsDebuggingEnabled(false)
        }
        settings.apply {
            setJavaScriptEnabled(true)
            setLoadsImagesAutomatically(true)
            setBlockNetworkImage(false)
            setLayoutAlgorithm(LWebSettings.LayoutAlgorithm.NARROW_COLUMNS)
            setSupportZoom(false)
            setBuiltInZoomControls(true)
            setUseWideViewPort(true)
            setDomStorageEnabled(true)
            setDatabaseEnabled(true)
            setCacheMode(WebSettings.LOAD_DEFAULT)
            setTextSize(LWebSettings.TextSize.NORMAL)
            setMediaPlaybackRequiresUserGesture(false)
            setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW)
            //在WebView中启用或禁用文件访问 Android11前默认为true，11后默认为false
            setAllowFileAccess(true)
            userAgentString = "$userAgentString BUZ"
        }
    }

    private fun initWebChromeClient() {
        setWebViewClient(object : LWebViewClient() {

            override fun shouldOverrideUrlLoading(
                view: LWebView,
                request: LWebResourceRequest
            ): Boolean {
                //logInfo(TAG, "setWebViewClient#shouldOverrideUrlLoading LWebResourceRequest:$request")
                if (request.urlString?.startsWith("intent://") == true) {
                    logInfo(TAG, "setWebViewClient#shouldOverrideUrlLoading intent:${request.url}")
                    request.urlString?.let {
                        val appInstalled = checkAppInstalledAndStartIntent(appContext, it)
                        logInfo(
                            TAG,
                            "setWebViewClient#shouldOverrideUrlLoading isAppInstalled:$appInstalled"
                        )
                    }
                    return true
                }
                return super.shouldOverrideUrlLoading(view, request)
            }
        })
        setWebChromeClient(object : LWebChromeClient() {
            override fun onReceivedTitle(view: LWebView?, title: String?) {
                super.onReceivedTitle(view, title)
                logInfo(TAG, "onReceivedTitle title:$title ")
                webChromeClientCallBack?.onReceivedTitle(view, title)
            }

            override fun onProgressChanged(view: LWebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                logInfo(TAG, "onProgressChanged newProgress:$newProgress ")
            }

            override fun onConsoleMessage(lConsoleMessage: LConsoleMessage?): Boolean {
                logInfo(TAG, "onConsoleMessage lConsoleMessage:$lConsoleMessage ")
                return super.onConsoleMessage(lConsoleMessage)
            }

            override fun onJsPrompt(
                view: LWebView?,
                url: String?,
                message: String?,
                defaultValue: String?,
                lResult: LJsPromptResult?
            ): Boolean {
                logInfo(TAG, "onJsPrompt message:$message ")
                return super.onJsPrompt(view, url, message, defaultValue, lResult)
            }

            override fun onJsConfirm(
                view: LWebView?,
                url: String?,
                message: String?,
                result: LJsResult?
            ): Boolean {
                logInfo(TAG, "onJsConfirm message:$message ")
                return super.onJsConfirm(view, url, message, result)
            }

            override fun onJsAlert(
                view: LWebView?,
                url: String?,
                message: String?,
                result: LJsResult?
            ): Boolean {
                logInfo(TAG, "onJsAlert message:$message ")
                return super.onJsAlert(view, url, message, result)
            }

            override fun onShowFileChooser(
                view: LWebView?,
                filePath: ValueCallback<Array<Uri>>?,
                params: LFileChooserParams?
            ): Boolean {
                logInfo(TAG, "onShowFileChooser filePath:$filePath ")
                fileChooserCallback?.invoke(filePath, params)
                return true
            }
        })
    }

    override fun loadJavascriptCallBack() {
        logInfo(TAG, "loadJavascriptCallBack isInject:${isInjectJs()}")
    }

    private fun checkAppInstalledAndStartIntent(context: Context, url: String): Boolean {
        val intent = Intent.parseUri(url, Intent.URI_INTENT_SCHEME)
        val resolveInfo = context.packageManager.resolveActivity(intent, 0)
        val appInstalled = resolveInfo != null
        if (appInstalled) {
            startActivity(intent)
        }
        return appInstalled
    }
}