package com.vocalbeats.scaffold.core.ui.navigation

import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.toRoute
import com.vocalbeats.scaffold.core.ui.WebViewScreen
import kotlinx.serialization.Serializable

/**
 * <AUTHOR>
 * @date 2025/8/13
 * @desc WebView 导航配置
 */

@Serializable
data class WebViewRoute(
    val url: String,
    val title: String = "",
)

/**
 * 导航到 WebView 页面
 */
fun NavController.navigateToWebView(url: String, title: String = "") {
    navigate(WebViewRoute(url = url, title = title))
}


/**
 * WebView 页面导航配置
 */
fun NavGraphBuilder.webViewScreen(
    onBackClick: () -> Unit,
) {
    composable<WebViewRoute> { backStackEntry ->
        val route = backStackEntry.toRoute<WebViewRoute>()
        WebViewScreen(
            url = route.url,
            title = route.title,
            onBackClick = onBackClick
        )
    }
}