package com.interfun.buz.common.web

import android.graphics.Color
import android.net.Uri
import android.view.View
import android.webkit.ValueCallback
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.R
import com.interfun.buz.common.base.binding.BaseBindingActivity
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.databinding.CommonActivityWebviewBinding
import com.interfun.buz.common.view.other.titleBar.IOnTitleBarListener
import com.interfun.buz.common.web.functions.JSFunctionData
import com.interfun.buz.common.web.manager.JSFunctionManager
import com.vocalbeats.scaffold.core.web.WebChromeClientCallBack
import com.yibasan.lizhifm.sdk.webview.LFileChooserParams
import com.yibasan.lizhifm.sdk.webview.LWebView
import com.yibasan.lizhifm.sdk.webview.WebViewManagerKt
import com.yibasan.lizhifm.sdk.webview.jswebview.bean.JsCallbackDetail
import com.yibasan.lizhifm.sdk.webview.jswebview.jsBridgeConfig
import com.yibasan.lizhifm.sdk.webview.jswebview.jsbridge.JsBridgeMessageListener

abstract class BaseWebViewActivity : BaseBindingActivity<CommonActivityWebviewBinding>() {
    init {
        initWebViewJSBridge()
    }

    companion object {
        const val TAG = "BaseWebViewActivity"

        private var isInit = false

        private fun initWebViewJSBridge() {
            logInfo(TAG, "initWebViewJSBridge isInit: $isInit")
            if (!isInit) {
                isInit = true
                WebViewManagerKt.withContext(appContext) {
                    jsBridgeConfig {
                        enableSimplyJsBridge()
                        verifyPassWhenDebug = isDebug
                        urlPatternWhiteList =
                            listOf("^https?://([a-zA-Z0-9\\._\\-]+\\.)?(lizhi\\.fm|lizhifm\\.com|buz-app\\.com|buzmenow\\.com|buz\\.ai)([?#/].*)?$")
                    }
                }
            }
        }
    }

    private val TAG = "BaseWebViewActivity"
    private val jsKeyboardUpMethod = "window.__native__keyboard_update && window.__native__keyboard_update(1);"
    private val jsKeyboardDownMethod = "window.__native__keyboard_update && window.__native__keyboard_update(0);"

    /**
     * 是否显示原生的标题栏 默认为false。如果为true [binding.headerView]会显示，反之亦然。
     */
    val inShowNativeTitleBar by lazy {
        intent.getBooleanExtra(
            RouterParamKey.WebView.KEY_SHOW_NATIVE_TITLE_BAR,
            false
        )
    }

    /**
     * 仅在[inShowNativeTitleBar]为true时生效。
     * 默认情况下：原生的标题栏内容会不断动态从webview加载的网址中获取，
     * 但是你传入这个参数时会固化为传入的标题
     */
    val inKeyForceTitle by lazy { intent.getStringExtra(RouterParamKey.WebView.KEY_FORCE_TITLE)?:"" }

    /**
     * 仅在[inShowNativeTitleBar]为true时生效
     * 默认情况下：原生的标题栏内容会不断动态从webview加载的网址中获取，但是加载网页需要一定时间。
     * 在此期间使用次参数的标题栏
     */
    val inPlaceholderTitle by lazy { intent.getStringExtra(RouterParamKey.WebView.KEY_PLACEHOLDER_TITLE)?:"" }

    private val webView get() = binding.webView
    private var callback: ValueCallback<Array<Uri>>? = null
    private val pickPictureLauncher = pickContentLauncher { uri -> handleChooseImage(uri) }
    private val photoPicker = registerForActivityResult(ActivityResultContracts.PickVisualMedia()) { uri ->
        handleChooseImage(uri)
    }

    override fun initView() {
        statusBarInit()
        initWebView()
        initTitleBar()
        initKeyboardObserver()
    }

    open fun statusBarInit(){
        binding.spaceStatusBar.initStatusBarHeight()
    }

    private fun initTitleBar() {
        binding.headerView.setOnTitleBarListener(object : IOnTitleBarListener {
            override fun onLeftClick(v: View?) {
                finishOrWebGoBack()
            }
            override fun onTitleClick(v: View?) {
            }
            override fun onRightClick(v: View?) {
            }
            override fun onSecRightClick(v: View?) {
            }
        })
    }

    override fun initData() {
        super.initData()
        //根据上层业务决定
        binding.headerView.visibleIf(inShowNativeTitleBar)
        if (inShowNativeTitleBar) {
            if (inKeyForceTitle.isEmpty()) {
                binding.headerView.setTitle(inPlaceholderTitle)
            } else {
                binding.headerView.setTitle(inKeyForceTitle)
            }
        }
        val url = intent.getStringExtra(RouterParamKey.Common.KEY_URL)
        if (!url.isNullOrEmpty()) {
            webView.loadUrl(url)
            log(TAG, "loadUrl: $url")
        }

    }

    private fun initKeyboardObserver() {
        observeKeyboardHeight(null){ _, isKeyboardShow ->
            if (isKeyboardShow){
                webView.evaluateJavascript(jsKeyboardUpMethod,null)
            }else{
                webView.evaluateJavascript(jsKeyboardDownMethod,null)
            }
        }
    }
    private fun initWebView(){
        webView.setBackgroundColor(Color.BLACK)
        webView.fileChooserCallback = { filePath, params ->
            onShowFileChooser(filePath, params)
        }
        webView.webChromeClientCallBack= object : WebChromeClientCallBack {
            override fun onReceivedTitle(view: LWebView?, title: String?) {
                //不显示标题直接不处理
                if (!inShowNativeTitleBar||title.isNullOrEmpty()) {
                    return
                }
                //update title
                //inKeyForceTitle是强制标题，如果设置那么不考虑网页标题
                if (inKeyForceTitle.isEmpty()) {
                    binding.headerView.setTitle(title)
                }
            }
        }
        webView.jsBridgeMessageListener = object : JsBridgeMessageListener() {
            /**
             * 接收到 js --> Native 调用的方法
             * 可以直接返回回调数据，
             * 或者后续带上 [callbackId] 调用 JsWebView的 [triggerJsCallback] 方法进行回调
             */
            override fun receiveMessage(
                method: String,
                params: String,
                callbackId: String
            ): JsCallbackDetail? {
                log(TAG, "receiveMessage method:$method params:$params callbackId:$callbackId")
                val data = JSFunctionData(method, params, callbackId)
                return JSFunctionManager.invoke(this@BaseWebViewActivity, webView, data)
            }
        }
    }

    private fun onShowFileChooser(
        filePath: ValueCallback<Array<Uri>>?,
        params: LFileChooserParams?
    ) {
        callback = filePath
        if (ActivityResultContracts.PickVisualMedia.isPhotoPickerAvailable()) {
            photoPicker.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
        } else {
            pickPictureLauncher.launchForImage()
        }
    }

    private fun handleChooseImage(uri: Uri?) {
        uri?.let {
            callback?.onReceiveValue(arrayOf(it))
        } ?: run {
            callback?.onReceiveValue(null)
        }
    }


    override fun onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            finish()
            overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
        }
    }

    protected open fun finishOrWebGoBack() {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            finishAfterTransition()
        }
    }
    override fun onDestroy() {
        super.onDestroy()
        webView.apply {
            // clearCache(true)
            removeAllViews()
            destroy()
        }
        callback = null
    }
}