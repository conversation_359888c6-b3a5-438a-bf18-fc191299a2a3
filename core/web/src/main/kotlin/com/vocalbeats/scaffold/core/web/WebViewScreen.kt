package com.vocalbeats.scaffold.core.web

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.vocalbeats.designtoken.DesignColors
import com.vocalbeats.designtoken.DesignFonts
import com.vocalbeats.designtoken.DesignIcons
import com.vocalbeats.designtoken.DesignIcons.Box
import com.vocalbeats.scaffold.core.common.compose.asDimension
import com.vocalbeats.scaffold.core.designsystem.component.IconFontText
import com.vocalbeats.scaffold.core.designsystem.component.WebView

/**
 * <AUTHOR>
 * @date 2025/8/13
 * @desc 通用 WebView 页面
 */
@Composable
fun WebViewScreen(
    url: String,
    title: String = "",
    onBackClick: () -> Unit = {},
    modifier: Modifier = Modifier,
    headers: Map<String, String> = emptyMap(),
    onWebViewCreated: () -> Unit = {},
    onWebViewRelease: () -> Unit = {},
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(DesignColors.white100)
            .statusBarsPadding()
    ) {
        // 顶部导航栏
        WebViewTopBar(
            title = title,
            onBackClick = onBackClick,
            modifier = Modifier.fillMaxWidth()
        )

        // WebView 内容区域
        WebView(
            url = url,
            headers = headers,
            onCreated = onWebViewCreated,
            onRelease = onWebViewRelease,
            modifier = Modifier
                .fillMaxSize()
                .weight(1f)
        )
    }
}

/**
 * WebView 顶部导航栏组件
 */
@Composable
private fun WebViewTopBar(
    title: String,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .height(R.dimen.title_bar_height.asDimension())
            .padding(horizontal = 20.dp),
        contentAlignment = Alignment.Center
    ) {
        // 返回按钮
        IconFontText(
            icon = DesignIcons.ArrowLeft,
            size = 24.dp,
            color = DesignColors.black80,
            onClick = onBackClick,
            modifier = Modifier.align(Alignment.CenterStart)
        )

        // 标题
        if (title.isNotEmpty()) {
            Text(
                text = title,
                style = DesignFonts.pjsMedium18(),
                color = DesignColors.black80,
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun WebViewScreenPreview() {
    WebViewScreen(
        url = "https://www.google.com",
        title = "Google",
        onBackClick = {}
    )
}