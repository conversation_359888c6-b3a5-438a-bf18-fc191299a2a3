package com.vocalbeats.scaffold.core.web.functions

import android.os.Build
import androidx.activity.ComponentActivity
import com.vocalbeats.scaffold.core.common.ktx.logInfo
import com.vocalbeats.scaffold.core.common.utils.ScreenUtil
import com.yibasan.lizhifm.sdk.platformtools.Const
import com.yibasan.lizhifm.sdk.platformtools.MobileUtils
import com.yibasan.lizhifm.sdk.webview.jswebview.LJavaScriptWebView
import com.yibasan.lizhifm.sdk.webview.jswebview.bean.JsCallbackDetail
import java.util.TimeZone

/**
 * <AUTHOR>
 * @date 2022/7/27
 * @desc
 */
internal class GetAppInfoFunction : JSFunction() {

    companion object{
        const val METHOD_NAME = "getAppInfo"
    }

    override fun invoke(
        activity: ComponentActivity,
        webView: LJavaScriptWebView,
        data: JSFunctionData
    ): JsCallbackDetail? {
        val callback = getSuccessCallback(data)
        callback.put("version", MobileUtils.getVersionNameFromManifest(activity))
        callback.put("statusHeight", ScreenUtil.px2dp(ScreenUtil.getStatusBarHeight(activity).toFloat(), activity) )
        callback.put("buildVersion", MobileUtils.getVersionCodeFromManifest(activity))
        callback.put("model", Build.MANUFACTURER + " " + Build.MODEL)
        callback.put("system", Const.deviceType)
        callback.put("deviceId", MobileUtils.getDeviceId())
        callback.put("status", "success")
        val displayName = TimeZone.getDefault().getDisplayName(true, TimeZone.SHORT)
        if (displayName.isNotEmpty()) {
            callback.put("timezone", displayName)
        }
        logInfo(TAG, "GetAppInfoFunction  >> callback = $callback")
        return callback
    }

}