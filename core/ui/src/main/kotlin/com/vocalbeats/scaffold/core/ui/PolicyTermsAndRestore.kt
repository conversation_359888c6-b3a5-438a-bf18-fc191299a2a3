package com.vocalbeats.scaffold.core.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.vocalbeats.designtoken.DesignColors
import com.vocalbeats.designtoken.DesignFonts
import com.vocalbeats.scaffold.core.common.R
import com.vocalbeats.scaffold.core.common.compose.asString
import com.vocalbeats.scaffold.core.common.compose.debouncedClickable

@Composable
fun PolicyTermsAndRestore(
    onPrivacyPolicyClick: () -> Unit = {},
    onTermsOfServiceClick: () -> Unit = {},
    onRestoreClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = R.string.guidance_privacy_policy.asString(),
            style = DesignFonts.pjsRegular12(),
            color = DesignColors.lightNtBk40,
            modifier = Modifier.debouncedClickable(onClick = onPrivacyPolicyClick)
        )

        Text(
            text = "  |  ",
            style = DesignFonts.pjsRegular12(),
            color = DesignColors.lightNtBk40
        )

        Text(
            text = R.string.guidance_terms_of_service.asString(),
            style = DesignFonts.pjsRegular12(),
            color = DesignColors.lightNtBk40,
            modifier = Modifier.debouncedClickable(onClick = onTermsOfServiceClick)
        )

        Text(
            text = "  |  ",
            style = DesignFonts.pjsRegular12(),
            color = DesignColors.lightNtBk40
        )

        Text(
            text = R.string.guidance_restore.asString(),
            style = DesignFonts.pjsRegular12(),
            color = DesignColors.lightNtBk40,
            modifier = Modifier.debouncedClickable(onClick = onRestoreClick)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun PolicyTermsAndRestorePreview() {
    PolicyTermsAndRestore({ 1f })
}