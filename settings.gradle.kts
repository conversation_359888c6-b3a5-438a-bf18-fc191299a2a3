import java.util.Properties

pluginManagement {
    includeBuild("build-logic")
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()

        maven {
            url = uri("http://maven.lizhi.fm:8081/nexus/content/repositories/releases/")
            isAllowInsecureProtocol = true
        }
        maven {
            url = uri("http://maven.lizhi.fm:8081/nexus/content/repositories/snapshots/")
            isAllowInsecureProtocol = true
        }
        maven {
            url = uri("http://maven.lizhi.fm:8081/nexus/content/groups/android_public/")
            isAllowInsecureProtocol = true
        }

        maven {
            url = uri("http://maven.lizhi.fm:8081/nexus/content/repositories/android_uploadpub/")
            isAllowInsecureProtocol = true
        }
    }

    // 添加 version catalogs 配置
    versionCatalogs {
        create("bizLibs") {
            from(files("gradle/bizlibs.versions.toml"))
        }
    }
}

// 读取项目配置并设置项目名称
val projectProperties = Properties().apply {
    val file = File(rootDir, "project.properties")
    if (file.exists()) {
        load(file.inputStream())
    } else {
        println("Properties file not found!")
    }
}

rootProject.name = projectProperties.getProperty("projectName", "androidscaffold")

enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")
include(":app")
include(":core:common")
include(":core:ui")
include(":core:data")
include(":core:model")
include(":core:database")
include(":core:datastore")
include(":feature:settings")
include(":core:designsystem")
include(":core:network")
include(":startup")
include(":feature:feedback")